#!/usr/bin/env python3
"""
Tkinter GUI for Image Variation Combiner

A user-friendly interface for combining image variations with customizable options.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
import math
from pathlib import Path
from typing import Optional, List, Tuple
from auto_variation_finder import ImageVariationFinder
from image_combiner import ImageCombiner

class ImageCombinerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Variation Combiner - 8K Output")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Variables
        self.input_dir = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.layout_mode = tk.StringVar(value="grid")
        self.batch_size = tk.IntVar(value=4)
        self.output_quality = tk.IntVar(value=95)
        # Removed target_resolution - now using dynamic sizing
        self.spacing = tk.IntVar(value=10)
        self.background_color = tk.StringVar(value="white")
        
        # CLIP variables
        self.clip_threshold = tk.DoubleVar(value=0.85)
        self.clip_model = tk.StringVar(value="ViT-B/32")
        self.device = tk.StringVar(value="auto")
        
        # Additional options
        self.generate_master_file = tk.BooleanVar(value=False)
        self.batch_file_path = tk.StringVar()
        self.import_mode = tk.BooleanVar(value=False)
        self.import_input_dir = tk.StringVar()
        
        # Progress tracking
        self.is_processing = False
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main container with padding
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Image Variation Combiner", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Input Directory Section
        ttk.Label(main_frame, text="Input Directory:", font=('Arial', 10, 'bold')).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=2, column=0, columnspan=3, sticky="we", pady=(0, 15))
        input_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(input_frame, textvariable=self.input_dir, width=60).grid(
            row=0, column=0, sticky="we", padx=(0, 10))
        ttk.Button(input_frame, text="Browse", 
                  command=self.browse_input_dir).grid(row=0, column=1)
        
        # Output Directory Section
        ttk.Label(main_frame, text="Output Directory:", font=('Arial', 10, 'bold')).grid(
            row=3, column=0, sticky=tk.W, pady=(0, 5))
        
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=4, column=0, columnspan=3, sticky="we", pady=(0, 15))
        output_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(output_frame, textvariable=self.output_dir, width=60).grid(
            row=0, column=0, sticky="we", padx=(0, 10))
        ttk.Button(output_frame, text="Browse", 
                  command=self.browse_output_dir).grid(row=0, column=1)
        
        # Settings Section
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding="15")
        settings_frame.grid(row=5, column=0, columnspan=3, sticky="we", pady=(0, 15))
        settings_frame.columnconfigure(1, weight=1)
        
        # Layout Mode
        ttk.Label(settings_frame, text="Layout Mode:").grid(row=0, column=0, sticky=tk.W, pady=5)
        layout_combo = ttk.Combobox(settings_frame, textvariable=self.layout_mode, 
                                   values=["grid", "horizontal", "vertical"], state="readonly")
        layout_combo.grid(row=0, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # Dynamic Sizing Info
        ttk.Label(settings_frame, text="Output Size:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Label(settings_frame, text="Dynamic (based on image content)", foreground="gray").grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Max Batch Size
        ttk.Label(settings_frame, text="Max Batch Size:").grid(row=2, column=0, sticky=tk.W, pady=5)
        batch_frame = ttk.Frame(settings_frame)
        batch_frame.grid(row=2, column=1, sticky="we", padx=(10, 0), pady=5)
        batch_spin = ttk.Spinbox(batch_frame, from_=2, to=16, textvariable=self.batch_size, width=10)
        batch_spin.grid(row=0, column=0, sticky=tk.W)
        ttk.Label(batch_frame, text="(similarity groups batches)", foreground="gray", font=('Arial', 8)).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # CLIP Model Selection
        ttk.Label(settings_frame, text="CLIP Model:").grid(row=3, column=0, sticky=tk.W, pady=5)
        model_combo = ttk.Combobox(settings_frame, textvariable=self.clip_model, 
                                  values=["ViT-B/32", "ViT-L/14", "RN50", "RN101", "RN50x4", "RN50x16", "RN50x64"], 
                                  state="readonly")
        model_combo.grid(row=3, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # Device Selection
        ttk.Label(settings_frame, text="Processing Device:").grid(row=4, column=0, sticky=tk.W, pady=5)
        device_combo = ttk.Combobox(settings_frame, textvariable=self.device,
                                   values=["auto", "cpu", "gpu", "mps", "cuda"], 
                                   state="readonly")
        device_combo.grid(row=4, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # CLIP Threshold
        ttk.Label(settings_frame, text="CLIP Similarity Threshold:").grid(row=5, column=0, sticky=tk.W, pady=5)
        threshold_frame = ttk.Frame(settings_frame)
        threshold_frame.grid(row=5, column=1, sticky="we", padx=(10, 0), pady=5)
        threshold_scale = ttk.Scale(threshold_frame, from_=0.5, to=0.95, variable=self.clip_threshold, 
                                   orient=tk.HORIZONTAL)
        threshold_scale.grid(row=0, column=0, sticky="we")
        threshold_frame.columnconfigure(0, weight=1)
        self.clip_threshold_label_main = ttk.Label(threshold_frame, text="0.85")
        self.clip_threshold_label_main.grid(row=0, column=1, padx=(10, 0))
        
        # Update threshold label when scale changes
        self.clip_threshold.trace_add('write', self.update_clip_threshold_label_main)
        
        # Spacing
        ttk.Label(settings_frame, text="Image Spacing (px):").grid(row=6, column=0, sticky=tk.W, pady=5)
        spacing_spin = ttk.Spinbox(settings_frame, from_=0, to=100, textvariable=self.spacing, width=10)
        spacing_spin.grid(row=6, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Background Color
        ttk.Label(settings_frame, text="Background Color:").grid(row=7, column=0, sticky=tk.W, pady=5)
        color_combo = ttk.Combobox(settings_frame, textvariable=self.background_color,
                                  values=["white", "black", "gray", "transparent"], state="readonly")
        color_combo.grid(row=7, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # Quality
        ttk.Label(settings_frame, text="Output Quality (%):").grid(row=8, column=0, sticky=tk.W, pady=5)
        quality_spin = ttk.Spinbox(settings_frame, from_=70, to=100, textvariable=self.output_quality, width=10)
        quality_spin.grid(row=8, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Additional Options
        ttk.Label(settings_frame, text="Generate Master File:").grid(row=9, column=0, sticky=tk.W, pady=5)
        master_check = ttk.Checkbutton(settings_frame, variable=self.generate_master_file)
        master_check.grid(row=9, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        ttk.Label(settings_frame, text="(Creates a single text file with all batches for easy importing)", 
                foreground="gray", font=('Arial', 8)).grid(row=9, column=1, sticky=tk.W, padx=(40, 0), pady=5)
        
        # Import from Batch File Section
        import_frame = ttk.LabelFrame(main_frame, text="Import From Batch File", padding="15")
        import_frame.grid(row=8, column=0, columnspan=3, sticky="we", pady=(0, 15))
        import_frame.columnconfigure(1, weight=1)
        
        # Import Mode Checkbox
        import_check = ttk.Checkbutton(import_frame, text="Use Import Mode", variable=self.import_mode, 
                                     command=self.toggle_import_mode)
        import_check.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        ttk.Label(import_frame, text="(Import and process batches from a previously created batch file)", 
                foreground="gray", font=('Arial', 8)).grid(row=0, column=1, sticky=tk.W, padx=(40, 0))
        
        # Batch File Selection
        ttk.Label(import_frame, text="Batch File:").grid(row=1, column=0, sticky=tk.W, pady=5)
        batch_file_frame = ttk.Frame(import_frame)
        batch_file_frame.grid(row=1, column=1, sticky="we")
        batch_file_frame.columnconfigure(0, weight=1)
        
        self.batch_file_entry = ttk.Entry(batch_file_frame, textvariable=self.batch_file_path, width=60, state="disabled")
        self.batch_file_entry.grid(row=0, column=0, sticky="we", padx=(0, 10))
        self.batch_file_button = ttk.Button(batch_file_frame, text="Browse", 
                                         command=self.browse_batch_file, state="disabled")
        self.batch_file_button.grid(row=0, column=1)
        
        # Import Input Directory
        ttk.Label(import_frame, text="Input Directory:").grid(row=2, column=0, sticky=tk.W, pady=5)
        import_input_frame = ttk.Frame(import_frame)
        import_input_frame.grid(row=2, column=1, sticky="we")
        import_input_frame.columnconfigure(0, weight=1)
        
        self.import_input_entry = ttk.Entry(import_input_frame, textvariable=self.import_input_dir, width=60, state="disabled")
        self.import_input_entry.grid(row=0, column=0, sticky="we", padx=(0, 10))
        self.import_input_button = ttk.Button(import_input_frame, text="Browse", 
                                           command=self.browse_import_input_dir, state="disabled")
        self.import_input_button.grid(row=0, column=1)
        ttk.Label(import_frame, text="(Required if the batch file contains only filenames without paths)", 
                foreground="gray", font=('Arial', 8)).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 5))
        
        # Info Section
        info_frame = ttk.LabelFrame(main_frame, text="Similarity Detection", padding="15")
        info_frame.grid(row=6, column=0, columnspan=3, sticky="we", pady=(0, 15))
        info_frame.columnconfigure(1, weight=1)
        
        # Info label for CLIP
        ttk.Label(info_frame, text="Detection Method:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Label(info_frame, text="CLIP (AI-based semantic similarity)", 
                 foreground="blue", font=('Arial', 10, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        self.similarity_info = ttk.Label(info_frame, text="Uses advanced AI to understand image content and find semantically similar images.", 
                                       foreground="gray", font=('Arial', 8))
        self.similarity_info.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))
        
        # Progress Section
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=7, column=0, columnspan=3, sticky="we", pady=(0, 15))
        progress_frame.columnconfigure(0, weight=1)

        self.progress_var = tk.StringVar(value="Ready to process images...")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)

        # Add detailed progress information
        self.detailed_progress_var = tk.StringVar(value="")
        self.detailed_progress_label = ttk.Label(progress_frame, textvariable=self.detailed_progress_var,
                                               foreground="gray", font=('Arial', 8))
        self.detailed_progress_label.grid(row=1, column=0, sticky=tk.W)

        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=2, column=0, sticky="we", pady=(5, 0))

        # Add performance metrics display
        self.performance_var = tk.StringVar(value="")
        self.performance_label = ttk.Label(progress_frame, textvariable=self.performance_var,
                                         foreground="blue", font=('Arial', 8))
        self.performance_label.grid(row=3, column=0, sticky=tk.W)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=9, column=0, columnspan=3, pady=(0, 10))
        
        self.process_button = ttk.Button(button_frame, text="Process Images", 
                                        command=self.start_processing, style='Accent.TButton')
        self.process_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Open Output Folder", 
                  command=self.open_output_folder).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Reset Settings", 
                  command=self.reset_settings).pack(side=tk.LEFT)
        
        # Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Processing Log", padding="10")
        log_frame.grid(row=10, column=0, columnspan=3, sticky="nsew", pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # Text widget with scrollbar
        text_frame = ttk.Frame(log_frame)
        text_frame.grid(row=0, column=0, sticky="nsew")
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(text_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        
        # Configure main frame row weights
        main_frame.rowconfigure(10, weight=1)
        
    def browse_input_dir(self):
        directory = filedialog.askdirectory(title="Select Input Directory")
        if directory:
            self.input_dir.set(directory)
            self.log(f"Input directory selected: {directory}")
    
    def browse_output_dir(self):
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir.set(directory)
            self.log(f"Output directory selected: {directory}")
    
    def browse_batch_file(self):
        """Browse for a batch text file"""
        file_path = filedialog.askopenfilename(
            title="Select Batch File", 
            filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")]
        )
        if file_path:
            self.batch_file_path.set(file_path)
            self.log(f"Batch file selected: {file_path}")
    
    def browse_import_input_dir(self):
        """Browse for input directory for batch file import"""
        directory = filedialog.askdirectory(title="Select Input Directory for Batch File Import")
        if directory:
            self.import_input_dir.set(directory)
            self.log(f"Import input directory selected: {directory}")
    
    def update_clip_threshold_label_main(self, *args):
        """Update the main CLIP threshold label"""
        self.clip_threshold_label_main.config(text=f"{self.clip_threshold.get():.2f}")
    
    def reset_settings(self):
        """Reset all settings to default values"""
        self.layout_mode.set("grid")
        self.batch_size.set(4)
        self.output_quality.set(95)
        self.spacing.set(10)
        self.background_color.set("white")
        self.clip_threshold.set(0.85)
        self.clip_model.set("ViT-B/32")
        self.device.set("auto")
        self.generate_master_file.set(False)
        self.import_mode.set(False)
        self.batch_file_path.set("")
        self.import_input_dir.set("")
        
        # Reset UI states for import mode
        self.toggle_import_mode()
        
        self.log("Settings reset to defaults")
    
    def open_output_folder(self):
        if self.output_dir.get() and os.path.exists(self.output_dir.get()):
            os.system(f'open "{self.output_dir.get()}"')
        else:
            messagebox.showwarning("Warning", "Output directory not found or not set")
    
    def log(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    # Removed get_target_size method - now using dynamic sizing
    
    def start_processing(self):
        if self.is_processing:
            return
        
        # Validate inputs
        if not self.input_dir.get():
            messagebox.showerror("Error", "Please select an input directory")
            return
        
        if not self.output_dir.get():
            messagebox.showerror("Error", "Please select an output directory")
            return
        
        if not os.path.exists(self.input_dir.get()):
            messagebox.showerror("Error", "Input directory does not exist")
            return
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir.get(), exist_ok=True)
        
        # Start processing in a separate thread
        self.is_processing = True
        self.process_button.config(state='disabled')
        self.progress_bar.start()
        
        thread = threading.Thread(target=self.process_images)
        thread.daemon = True
        thread.start()
    
    def process_images(self):
        try:
            self.progress_var.set("Initializing image processing...")
            self.log("=" * 50)
            self.log("Starting image processing...")
            
            # Check if we're in import mode
            if self.import_mode.get():
                self._process_from_batch_file()
            else:
                self._process_from_directory()
                
        except Exception as e:
            error_msg = f"Error during processing: {str(e)}"
            self.log(error_msg)
            self.progress_var.set("Processing failed")
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))
        
        finally:
            # Reset UI state
            self.is_processing = False
            self.root.after(0, lambda: [
                self.progress_bar.stop(),
                self.process_button.config(state='normal'),
                self.progress_var.set("Ready to process images...")
            ])
    
    def _process_from_batch_file(self):
        """Process batches from a batch file"""
        # Validate inputs
        if not self.batch_file_path.get():
            messagebox.showerror("Error", "Please select a batch file")
            return
        
        if not self.output_dir.get():
            messagebox.showerror("Error", "Please select an output directory")
            return
        
        if not os.path.exists(self.batch_file_path.get()):
            messagebox.showerror("Error", "Batch file does not exist")
            return
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir.get(), exist_ok=True)
        
        # Check if the input directory is needed
        input_dir = self.import_input_dir.get() if self.import_input_dir.get() else None
        
        # Start processing in a separate thread
        self.is_processing = True
        self.process_button.config(state='disabled')
        self.progress_bar.start()
        
        self.log(f"Processing from batch file: {self.batch_file_path.get()}")
        if input_dir:
            self.log(f"Using input directory: {input_dir}")
        self.log(f"Layout mode: {self.layout_mode.get()}")
        self.log(f"Spacing: {self.spacing.get()}px")
        self.log("Using dynamic output size based on image content")
        
        # Import and process batches
        from image_combiner import ImageCombiner
        try:
            ImageCombiner.process_from_batch_file(
                batch_file_path=self.batch_file_path.get(),
                output_dir=self.output_dir.get(),
                input_dir=input_dir,
                layout_mode=self.layout_mode.get(),
                spacing=self.spacing.get(),
                background_color=self.background_color.get(),
                quality=self.output_quality.get(),
                resize=None  # Use dynamic sizing
            )
            
            self.progress_var.set("Import processing complete!")
            self.log("\nImport processing complete!")
            self.log(f"Output saved to: {self.output_dir.get()}")
            
            # Show completion message
            self.root.after(0, lambda: messagebox.showinfo(
                "Success", 
                f"Import processing complete!\n\nProcessed batches from file:\n{self.batch_file_path.get()}\n\nUsing dynamic output resolution\n\nCheck the output directory for results."
            ))
            
        except Exception as e:
            error_msg = f"Error processing batch file: {str(e)}"
            self.log(error_msg)
            raise
    
    def _process_from_directory(self):
        """Process images from input directory (original process)"""
        # Validate inputs
        if not self.input_dir.get():
            messagebox.showerror("Error", "Please select an input directory")
            return
        
        if not self.output_dir.get():
            messagebox.showerror("Error", "Please select an output directory")
            return
        
        if not os.path.exists(self.input_dir.get()):
            messagebox.showerror("Error", "Input directory does not exist")
            return
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir.get(), exist_ok=True)
        
        # Start processing in a separate thread
        self.is_processing = True
        self.process_button.config(state='disabled')
        self.progress_bar.start()
        
        # Use dynamic sizing based on actual image content
        # No fixed target size - let the combiner determine optimal size
        effective_spacing = self.spacing.get()
        
        self.log(f"Layout mode: {self.layout_mode.get()} (only applies to groups of similar images)")
        self.log(f"Max batch size: {self.batch_size.get()} (similarity-based grouping)")
        self.log(f"Spacing: {effective_spacing}px")
        self.log("Using dynamic output size based on image content")
        self.log("Single images with no variations will be preserved as-is (no grid or resizing)")
        if self.generate_master_file.get():
            self.log("Will generate master file with all batch information")
        
        # Initialize the variation finder with progress callback
        finder = ImageVariationFinder(
            clip_threshold=self.clip_threshold.get(),
            clip_model_name=self.clip_model.get(),
            device=self.device.get(),
            progress_callback=self._update_progress_from_finder
        )
        
        # Log similarity method being used
        self.log(f"Using CLIP model: {self.clip_model.get()} on {self.device.get()} (threshold: {self.clip_threshold.get():.2f})")
        self.log("Using improved clustering algorithm for better accuracy with larger batches")

        # Provide optimization recommendations for large datasets
        self._log_optimization_recommendations(finder)

        # Configure performance settings based on user preferences
        self._configure_performance_settings(finder)
        
        # Process the directory
        self.progress_var.set("Scanning for similar images...")
        batches = finder.process_directory(
            input_dir=self.input_dir.get(),
            output_dir=self.output_dir.get(),
            batch_size=self.batch_size.get(),
            target_size=None,  # Use dynamic sizing based on image content
            layout_mode=self.layout_mode.get(),
            spacing=effective_spacing,
            background_color=self.background_color.get(),
            quality=self.output_quality.get(),
            generate_master_file=self.generate_master_file.get()
        )
        
        self.progress_var.set(f"Processing complete!")
        self.log(f"\nProcessing complete!")
        
        # Count the total number of processed images
        total_images = sum(len(batch) for batch in batches) if batches else 0
        
        # Additional images may have been processed as individual files
        input_dir = self.input_dir.get()
        from pathlib import Path
        img_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp']
        all_images = []
        for ext in img_extensions:
            all_images.extend(list(Path(input_dir).glob(f"**/*{ext}")))
            all_images.extend(list(Path(input_dir).glob(f"**/*{ext.upper()}")))
        
        total_input_images = len(set(str(p) for p in all_images))
        
        # Count single image batches
        single_image_batches = sum(1 for batch in batches if batch and len(batch) == 1) if batches else 0
        multi_image_batches = len(batches) - single_image_batches if batches else 0
        
        self.log(f"Total input images: {total_input_images}")
        self.log(f"Generated {len(batches) if batches else 0} image batches")
        if single_image_batches > 0:
            self.log(f"- {single_image_batches} single images preserved as-is")
        if multi_image_batches > 0:
            self.log(f"- {multi_image_batches} groups of similar images combined")
        self.log(f"Output saved to: {self.output_dir.get()}")
        
        # Show completion message
        self.root.after(0, lambda: messagebox.showinfo(
            "Success", 
            f"Processing complete!\n\nProcessed {total_input_images} input images\n"
            f"Generated {len(batches) if batches else 0} batches:\n"
            f"- {single_image_batches} single images preserved as-is\n"
            f"- {multi_image_batches} similar image groups combined\n\n"
            f"Using dynamic output resolution\n\nCheck the output directory for results."
        ))
    
    def toggle_import_mode(self):
        """Toggle between normal mode and import mode"""
        import_mode = self.import_mode.get()
        
        # Enable/disable input directory controls
        state = "disabled" if import_mode else "normal"
        self.root.nametowidget('.!frame.!frame2.!entry').configure(state=state)  # Input dir entry
        self.root.nametowidget('.!frame.!frame2.!button').configure(state=state)  # Input dir browse button
        
        # Enable/disable batch file controls
        batch_state = "normal" if import_mode else "disabled"
        self.batch_file_entry.configure(state=batch_state)
        self.batch_file_button.configure(state=batch_state)
        self.import_input_entry.configure(state=batch_state)
        self.import_input_button.configure(state=batch_state)
        
        # Update log
        if import_mode:
            self.log("Switched to Import Mode - will process batches from a text file")
        else:
            self.log("Switched to Normal Mode - will process images from input directory")

    def _log_optimization_recommendations(self, finder):
        """Log optimization recommendations based on dataset size."""
        try:
            # Count images in input directory
            input_dir = self.input_dir.get()
            if not input_dir or not os.path.exists(input_dir):
                return

            from pathlib import Path
            img_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp']
            all_images = []
            for ext in img_extensions:
                all_images.extend(list(Path(input_dir).glob(f"**/*{ext}")))
                all_images.extend(list(Path(input_dir).glob(f"**/*{ext.upper()}")))

            num_images = len(set(str(p) for p in all_images))

            if num_images > 100:
                self.log(f"Large dataset detected: {num_images} images")

                # Get optimization recommendations
                recommendations = finder.optimize_for_dataset_size(num_images)
                memory_estimate = finder.get_memory_usage_estimate(num_images)

                self.log(f"Estimated memory usage: {memory_estimate}")
                self.log(f"Recommended batch size: {recommendations['batch_size']}")
                self.log(f"Recommended threshold: {recommendations['clip_threshold']}")

                if num_images > 1000:
                    self.log("⚠️  Very large dataset! Processing may take significant time.")
                    self.log("💡 Consider processing in smaller chunks if you encounter memory issues.")

                # Show recommendations for very large datasets without auto-adjusting
                if num_images > 500 and self.batch_size.get() < recommendations['batch_size']:
                    self.log(f"💡 Performance tip: Consider increasing batch size to {recommendations['batch_size']} for better performance with large datasets (current: {self.batch_size.get()})")

                if num_images > 500 and self.clip_threshold.get() > recommendations['clip_threshold']:
                    self.log(f"💡 Performance tip: Consider lowering CLIP threshold to {recommendations['clip_threshold']:.2f} for better accuracy with large datasets (current: {self.clip_threshold.get():.2f})")

                # Confirm user's settings will be respected
                self.log(f"✅ Using your selected batch size: {self.batch_size.get()} (user choice will be respected)")

        except Exception as e:
            self.log(f"Could not analyze dataset size: {e}")

    def _configure_performance_settings(self, finder):
        """Configure performance settings based on user preferences and dataset size."""
        try:
            # Count images in input directory
            input_dir = self.input_dir.get()
            if not input_dir or not os.path.exists(input_dir):
                return

            from pathlib import Path
            img_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp']
            all_images = []
            for ext in img_extensions:
                all_images.extend(list(Path(input_dir).glob(f"**/*{ext}")))
                all_images.extend(list(Path(input_dir).glob(f"**/*{ext.upper()}")))

            num_images = len(set(str(p) for p in all_images))

            # Configure performance settings based on dataset size
            if num_images > 2000:
                # Ultra-large dataset - very conservative settings
                finder.configure_performance_settings(
                    max_refinement_time_minutes=30,
                    enable_memory_optimization=True,
                    enable_early_termination=True
                )
                self.log("🔧 Configured for ultra-large dataset: Extended timeout, aggressive memory optimization")
            elif num_images > 1000:
                # Large dataset - balanced settings
                finder.configure_performance_settings(
                    max_refinement_time_minutes=20,
                    enable_memory_optimization=True,
                    enable_early_termination=True
                )
                self.log("🔧 Configured for large dataset: Extended timeout, memory optimization enabled")
            elif num_images > 500:
                # Medium dataset - standard settings with optimizations
                finder.configure_performance_settings(
                    max_refinement_time_minutes=15,
                    enable_memory_optimization=True,
                    enable_early_termination=True
                )
                self.log("🔧 Configured for medium dataset: Standard timeout, optimizations enabled")
            else:
                # Small dataset - fast settings
                finder.configure_performance_settings(
                    max_refinement_time_minutes=10,
                    enable_memory_optimization=False,
                    enable_early_termination=False
                )
                self.log("🔧 Configured for small dataset: Fast processing, minimal optimizations")

        except Exception as e:
            self.log(f"Could not configure performance settings: {e}")

    def _update_progress_from_finder(self, message: str):
        """Callback method to receive progress updates from the ImageVariationFinder."""
        # Parse the message to extract different types of information
        if ":" in message and ("%" in message or "ETA:" in message):
            # This is a detailed progress message
            parts = message.split(" - ")
            main_progress = parts[0] if parts else message
            details = " - ".join(parts[1:]) if len(parts) > 1 else ""

            # Update main progress
            self.root.after(0, lambda: self.progress_var.set(main_progress))

            # Update detailed progress
            if details:
                self.root.after(0, lambda: self.detailed_progress_var.set(details))

            # Extract performance metrics if available
            if "Memory:" in message:
                memory_part = [part for part in parts if "Memory:" in part]
                if memory_part:
                    self.root.after(0, lambda: self.performance_var.set(memory_part[0]))
        else:
            # Simple status message
            self.root.after(0, lambda: self.progress_var.set(message))

        # Always log the full message
        self.root.after(0, lambda: self.log(message))

        # Update the GUI to show the progress
        self.root.after(0, lambda: self.root.update_idletasks())

def main():
    root = tk.Tk()
    
    # Set up modern styling
    style = ttk.Style()
    
    # Try to use a modern theme
    available_themes = style.theme_names()
    if 'aqua' in available_themes:  # macOS
        style.theme_use('aqua')
    elif 'vista' in available_themes:  # Windows
        style.theme_use('vista')
    elif 'clam' in available_themes:  # Cross-platform modern
        style.theme_use('clam')
    
    app = ImageCombinerGUI(root)
    
    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()