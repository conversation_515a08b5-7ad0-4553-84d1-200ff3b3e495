#!/usr/bin/env python3
"""
Test script to verify that batch creation logic works correctly
and never skips creating batches when similar images are found.
"""

import os
import sys
from auto_variation_finder import ImageVariationFinder

def test_batch_creation():
    """Test the batch creation logic with different scenarios."""
    
    print("Testing batch creation logic...")
    print("=" * 50)
    
    # Create a mock finder instance
    finder = ImageVariationFinder(clip_threshold=0.85)
    
    # Test Case 1: Group of 8 images with max batch size 4
    print("\nTest Case 1: 8 similar images, max batch size 4")
    print("Expected: 2 batches of 4 images each")
    
    similar_groups_1 = {
        "image1.jpg": ["image1.jpg", "image2.jpg", "image3.jpg", "image4.jpg", 
                       "image5.jpg", "image6.jpg", "image7.jpg", "image8.jpg"]
    }
    
    batches_1 = finder.create_batches(similar_groups_1, batch_size=4)
    print(f"Result: {len(batches_1)} batches created")
    for i, batch in enumerate(batches_1, 1):
        print(f"  Batch {i}: {len(batch)} images")
    
    # Verify results
    total_images_1 = sum(len(batch) for batch in batches_1)
    max_batch_size_1 = max(len(batch) for batch in batches_1) if batches_1 else 0
    
    print(f"✅ Total images processed: {total_images_1}/8")
    print(f"✅ Max batch size: {max_batch_size_1} (should be ≤ 4)")
    print(f"✅ No images skipped: {total_images_1 == 8}")
    
    # Test Case 2: Group of 10 images with max batch size 4
    print("\nTest Case 2: 10 similar images, max batch size 4")
    print("Expected: 3 batches (4, 4, 2 images)")
    
    similar_groups_2 = {
        "imageA.jpg": ["imageA.jpg", "imageB.jpg", "imageC.jpg", "imageD.jpg", 
                       "imageE.jpg", "imageF.jpg", "imageG.jpg", "imageH.jpg",
                       "imageI.jpg", "imageJ.jpg"]
    }
    
    batches_2 = finder.create_batches(similar_groups_2, batch_size=4)
    print(f"Result: {len(batches_2)} batches created")
    for i, batch in enumerate(batches_2, 1):
        print(f"  Batch {i}: {len(batch)} images")
    
    # Verify results
    total_images_2 = sum(len(batch) for batch in batches_2)
    max_batch_size_2 = max(len(batch) for batch in batches_2) if batches_2 else 0
    
    print(f"✅ Total images processed: {total_images_2}/10")
    print(f"✅ Max batch size: {max_batch_size_2} (should be ≤ 4)")
    print(f"✅ No images skipped: {total_images_2 == 10}")
    
    # Test Case 3: Multiple groups with different sizes
    print("\nTest Case 3: Multiple groups with different sizes")
    print("Expected: All groups processed, none skipped")
    
    similar_groups_3 = {
        "group1_rep.jpg": ["group1_1.jpg", "group1_2.jpg", "group1_3.jpg"],  # 3 images
        "group2_rep.jpg": ["group2_1.jpg", "group2_2.jpg", "group2_3.jpg", "group2_4.jpg", 
                          "group2_5.jpg", "group2_6.jpg"],  # 6 images -> should split to 4+2
        "group3_rep.jpg": ["group3_1.jpg", "group3_2.jpg"],  # 2 images
        "group4_rep.jpg": ["group4_1.jpg"]  # 1 image
    }
    
    batches_3 = finder.create_batches(similar_groups_3, batch_size=4)
    print(f"Result: {len(batches_3)} batches created")
    for i, batch in enumerate(batches_3, 1):
        print(f"  Batch {i}: {len(batch)} images")
    
    # Verify results
    total_images_3 = sum(len(batch) for batch in batches_3)
    expected_images_3 = sum(len(group) for group in similar_groups_3.values())
    max_batch_size_3 = max(len(batch) for batch in batches_3) if batches_3 else 0
    
    print(f"✅ Total images processed: {total_images_3}/{expected_images_3}")
    print(f"✅ Max batch size: {max_batch_size_3} (should be ≤ 4)")
    print(f"✅ No images skipped: {total_images_3 == expected_images_3}")
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY:")
    
    all_tests_passed = (
        total_images_1 == 8 and max_batch_size_1 <= 4 and
        total_images_2 == 10 and max_batch_size_2 <= 4 and
        total_images_3 == expected_images_3 and max_batch_size_3 <= 4
    )
    
    if all_tests_passed:
        print("✅ ALL TESTS PASSED: Batch creation works correctly!")
        print("✅ No images are skipped when similar groups are found")
        print("✅ All batches respect the maximum batch size limit")
        print("✅ Large groups are properly split into multiple batches")
    else:
        print("❌ SOME TESTS FAILED: There are issues with batch creation")
        
    return all_tests_passed

if __name__ == "__main__":
    test_batch_creation()
