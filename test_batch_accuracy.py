#!/usr/bin/env python3
"""
Test script to validate batch accuracy improvements.
Tests the improved similarity grouping and batch splitting algorithms.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import random
import math

# Add current directory to path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from auto_variation_finder import ImageVariationFinder

class BatchAccuracyTester:
    def __init__(self):
        self.test_dir = None
        self.output_dir = None
        
    def create_test_images(self, num_groups=5, images_per_group=8, image_size=(300, 300)):
        """
        Create synthetic test images with known similarity groups.
        Each group will have similar colors/patterns to test similarity detection.
        """
        self.test_dir = tempfile.mkdtemp(prefix="batch_accuracy_test_")
        print(f"Creating test images in: {self.test_dir}")
        
        # Define more distinct patterns and colors for different groups
        patterns = [
            # Red geometric patterns
            {"base_color": (200, 50, 50), "pattern": "rectangles", "accent": (255, 100, 100)},
            # Blue circular patterns
            {"base_color": (50, 50, 200), "pattern": "circles", "accent": (100, 100, 255)},
            # Green diagonal patterns
            {"base_color": (50, 150, 50), "pattern": "lines", "accent": (100, 255, 100)},
            # Purple mixed patterns
            {"base_color": (150, 50, 150), "pattern": "mixed", "accent": (200, 100, 200)},
            # Orange gradient patterns
            {"base_color": (200, 100, 50), "pattern": "gradient", "accent": (255, 150, 100)},
        ]
        
        created_groups = {}
        
        for group_idx in range(min(num_groups, len(patterns))):
            group_name = f"group_{group_idx + 1}"
            created_groups[group_name] = []
            pattern_info = patterns[group_idx]

            for img_idx in range(images_per_group):
                # Create image with distinct patterns for each group
                img = Image.new('RGB', image_size, pattern_info["base_color"])
                draw = ImageDraw.Draw(img)

                # Create distinct patterns based on group type
                if pattern_info["pattern"] == "rectangles":
                    # Draw rectangles in a grid pattern
                    for i in range(3):
                        for j in range(3):
                            x1 = i * (image_size[0] // 4) + random.randint(0, 20)
                            y1 = j * (image_size[1] // 4) + random.randint(0, 20)
                            x2 = x1 + 40 + random.randint(0, 20)
                            y2 = y1 + 40 + random.randint(0, 20)

                            color_var = tuple(max(0, min(255, c + random.randint(-20, 20)))
                                            for c in pattern_info["accent"])
                            draw.rectangle([x1, y1, x2, y2], fill=color_var)

                elif pattern_info["pattern"] == "circles":
                    # Draw circles in a pattern
                    for i in range(4):
                        for j in range(4):
                            x = i * (image_size[0] // 5) + random.randint(10, 30)
                            y = j * (image_size[1] // 5) + random.randint(10, 30)
                            r = 15 + random.randint(0, 10)

                            color_var = tuple(max(0, min(255, c + random.randint(-20, 20)))
                                            for c in pattern_info["accent"])
                            draw.ellipse([x-r, y-r, x+r, y+r], fill=color_var)

                elif pattern_info["pattern"] == "lines":
                    # Draw diagonal lines
                    for i in range(8):
                        x1 = random.randint(0, image_size[0])
                        y1 = 0
                        x2 = random.randint(0, image_size[0])
                        y2 = image_size[1]

                        color_var = tuple(max(0, min(255, c + random.randint(-20, 20)))
                                        for c in pattern_info["accent"])
                        draw.line([x1, y1, x2, y2], fill=color_var, width=3)

                elif pattern_info["pattern"] == "mixed":
                    # Mix of shapes
                    for _ in range(6):
                        if random.choice([True, False]):
                            # Rectangle
                            x1, y1 = random.randint(0, image_size[0]//2), random.randint(0, image_size[1]//2)
                            x2, y2 = x1 + random.randint(30, 60), y1 + random.randint(30, 60)
                            color_var = tuple(max(0, min(255, c + random.randint(-20, 20)))
                                            for c in pattern_info["accent"])
                            draw.rectangle([x1, y1, x2, y2], fill=color_var)
                        else:
                            # Circle
                            x, y = random.randint(30, image_size[0]-30), random.randint(30, image_size[1]-30)
                            r = random.randint(15, 25)
                            color_var = tuple(max(0, min(255, c + random.randint(-20, 20)))
                                            for c in pattern_info["accent"])
                            draw.ellipse([x-r, y-r, x+r, y+r], fill=color_var)

                # Save image
                filename = f"{group_name}_image_{img_idx + 1:02d}.png"
                filepath = os.path.join(self.test_dir, filename)
                img.save(filepath)
                created_groups[group_name].append(filepath)
        
        print(f"Created {num_groups} groups with {images_per_group} images each")
        return created_groups
    
    def test_similarity_detection(self, expected_groups, clip_threshold=0.75):
        """
        Test the similarity detection algorithm.
        """
        print(f"\n=== Testing Similarity Detection (threshold: {clip_threshold}) ===")
        
        # Initialize finder
        finder = ImageVariationFinder(
            clip_threshold=clip_threshold,
            clip_model_name="ViT-B/32",
            device="auto"
        )
        
        # Get all image paths
        all_images = []
        for group_images in expected_groups.values():
            all_images.extend(group_images)
        
        # Find similar images
        detected_groups = finder.find_similar_images(all_images)
        
        print(f"Expected {len(expected_groups)} groups, detected {len(detected_groups)} groups")
        
        # Analyze accuracy
        self._analyze_grouping_accuracy(expected_groups, detected_groups)
        
        return detected_groups
    
    def test_batch_creation(self, detected_groups, batch_size=4):
        """
        Test the batch creation with intelligent splitting.
        """
        print(f"\n=== Testing Batch Creation (max batch size: {batch_size}) ===")
        
        finder = ImageVariationFinder(clip_threshold=0.75)
        
        # Create batches
        batches = finder.create_batches(detected_groups, batch_size)
        
        print(f"Created {len(batches)} batches from {len(detected_groups)} groups")
        
        # Analyze batch quality
        self._analyze_batch_quality(batches, batch_size)
        
        return batches
    
    def _analyze_grouping_accuracy(self, expected_groups, detected_groups):
        """
        Analyze how well the detected groups match expected groups.
        """
        print("\nGrouping Analysis:")
        
        # Create mapping from image to expected group
        image_to_expected = {}
        for group_name, images in expected_groups.items():
            for img in images:
                image_to_expected[img] = group_name
        
        # Analyze each detected group
        correct_groups = 0
        mixed_groups = 0
        
        for representative, group_images in detected_groups.items():
            if len(group_images) == 1:
                continue  # Skip single-image groups
                
            # Check if all images in this group belong to the same expected group
            expected_group_names = set()
            for img in group_images:
                if img in image_to_expected:
                    expected_group_names.add(image_to_expected[img])
            
            if len(expected_group_names) == 1:
                correct_groups += 1
                print(f"✓ Correct group: {len(group_images)} images from {list(expected_group_names)[0]}")
            else:
                mixed_groups += 1
                print(f"✗ Mixed group: {len(group_images)} images from {expected_group_names}")
        
        accuracy = correct_groups / (correct_groups + mixed_groups) if (correct_groups + mixed_groups) > 0 else 0
        print(f"\nGrouping Accuracy: {accuracy:.2%} ({correct_groups}/{correct_groups + mixed_groups})")
    
    def _analyze_batch_quality(self, batches, max_batch_size):
        """
        Analyze the quality of created batches.
        """
        print("\nBatch Quality Analysis:")
        
        oversized_batches = 0
        total_images = 0
        batch_sizes = []
        
        for i, batch in enumerate(batches):
            batch_size = len(batch)
            batch_sizes.append(batch_size)
            total_images += batch_size
            
            if batch_size > max_batch_size:
                oversized_batches += 1
                print(f"✗ Batch {i+1}: {batch_size} images (exceeds max size {max_batch_size})")
            else:
                print(f"✓ Batch {i+1}: {batch_size} images")
        
        avg_batch_size = np.mean(batch_sizes)
        print(f"\nBatch Statistics:")
        print(f"- Total batches: {len(batches)}")
        print(f"- Total images: {total_images}")
        print(f"- Average batch size: {avg_batch_size:.1f}")
        print(f"- Oversized batches: {oversized_batches}")
        print(f"- Size compliance: {((len(batches) - oversized_batches) / len(batches) * 100):.1f}%")
    
    def run_comprehensive_test(self):
        """
        Run a comprehensive test of all improvements.
        """
        print("=== Comprehensive Batch Accuracy Test ===")
        
        try:
            # Create test images
            expected_groups = self.create_test_images(
                num_groups=4, 
                images_per_group=6,
                image_size=(200, 200)
            )
            
            # Test with different thresholds and batch sizes
            test_configs = [
                {"threshold": 0.7, "batch_size": 4},
                {"threshold": 0.8, "batch_size": 6},
                {"threshold": 0.75, "batch_size": 8},
            ]
            
            for config in test_configs:
                print(f"\n{'='*60}")
                print(f"Testing with threshold={config['threshold']}, batch_size={config['batch_size']}")
                print(f"{'='*60}")
                
                # Test similarity detection
                detected_groups = self.test_similarity_detection(
                    expected_groups, 
                    config['threshold']
                )
                
                # Test batch creation
                batches = self.test_batch_creation(
                    detected_groups, 
                    config['batch_size']
                )
            
            print(f"\n=== Test Complete ===")
            print(f"Test images created in: {self.test_dir}")
            print("You can manually inspect the test images to verify the results.")
            
        except Exception as e:
            print(f"Test failed with error: {e}")
            import traceback
            traceback.print_exc()
        
    def cleanup(self):
        """Clean up test files."""
        if self.test_dir and os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
            print(f"Cleaned up test directory: {self.test_dir}")

def main():
    tester = BatchAccuracyTester()
    
    try:
        tester.run_comprehensive_test()
    finally:
        # Uncomment the next line if you want to automatically clean up test files
        # tester.cleanup()
        pass

if __name__ == "__main__":
    main()
